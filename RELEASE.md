# Library Release Strategy

This document describes the branching model, CI/CD pipelines, and versioning approach for publishing the Fincloud UI library to the internal Nexus NPM registry. The strategy ensures that development changes can be tested early while maintaining stability for production releases.

## Table of Contents

- [Branching Model](#branching-model)
- [CI/CD Pipeline Overview](#cicd-pipeline-overview)
- [Release Flow](#release-flow)
- [Manual Release Procedures](#manual-release-procedures)
- [Bug Fix Policy](#bug-fix-policy)
- [Version Management](#version-management)

---

## Branching Model

We maintain two permanent branches with specific purposes and automated publishing:

### 1. **`development`** Branch

- **Purpose:** Main integration branch for new features and changes
- **Usage:** Ongoing development work that may not be production-ready
- **Publishing:** Automatically publishes to **`next`** tag on every push/merge
- **Version Format:** `x.x.x-next.x` (e.g., `1.2.0-next.1`)
- **Target Audience:** Development teams for early testing and integration

### 2. **`master`** Branch

- **Purpose:** Stable, production-ready code
- **Usage:** Only updated via merges from `development` or hotfixes
- **Publishing:** Automatically publishes to **`latest`** tag on every push/merge
- **Version Format:** Standard semantic versioning `x.y.z` (e.g., `1.2.0`)
- **Target Audience:** QA, staging, and production environments

---

## CI/CD Pipeline Overview

Both branches trigger automated GitLab CI/CD pipelines defined in `.gitlab-ci.yml`.

### Development Branch Pipeline (`publish_npm_package_next`)

**Trigger:** Any push or merge to `development` branch

**Pipeline Steps:**

1. **Environment Setup**

   - Uses Node.js 18 Docker image
   - Configures Git credentials for automated commits
   - Sets up authentication for private registry

2. **Version Management**

   ```bash
   npx nx release version prerelease --preid=next --git-commit=true --git-tag=true
   ```

   - Increments pre-release version (e.g., `1.2.0-next.1` → `1.2.0-next.2`)
   - Creates Git commit and tag automatically

3. **Build and Publish**

   ```bash
   npm run build  # Executes: nx run ui:prepare-publish
   npx nx release publish
   ```

   - Builds the library with production configuration
   - Publishes to Nexus registry with `next` tag

4. **Git Operations**
   - Pushes version commit and tags back to repository
   - Uses `ci.skip` to prevent recursive pipeline triggers

### Master Branch Pipeline (`publish_npm_package`)

**Trigger:** Any push or merge to `master` branch

**Pipeline Steps:**

1. **Environment Setup** (same as development)

2. **Version Management**

   ```bash
   npx nx release version minor --git-commit=true --git-tag=true
   ```

   - Increments minor version (e.g., `1.2.0` → `1.3.0`)
   - Creates Git commit and tag automatically

3. **Build and Publish**

   ```bash
   npm run build
   npx nx release publish
   ```

   - Builds the library with production configuration
   - Publishes to Nexus registry with `latest` tag

4. **Git Operations** (same as development)

---

## Release Flow

1. During a **web application release**, the `development` branch of the library is merged into `master`.
2. The `master` branch pipeline runs and publishes a new **`latest`** version.
3. The new package version is updated in the web application’s `package.json`.
4. QA validates the release.

### Release Timeline Example

```
development: 1.2.0-next.1 → 1.2.0-next.2 → 1.2.0-next.3
                                                    ↓ (merge to master)
master:                                           1.3.0
```

---

## Manual Release Procedures

### Emergency Manual Release

If automated pipelines fail or manual intervention is required:

#### For Development (next) Release

```bash
# 1. Ensure you're on development branch
git checkout development
git pull origin development

# 2. Install dependencies
npm install

# 3. Create pre-release version
npx nx release version prerelease --preid=next --git-commit=true --git-tag=true

# 4. Build the library
npm run build

# 5. Publish to registry
npx nx release publish

# 6. Push changes
git push origin development --tags
```

#### For Production (latest) Release

```bash
# 1. Ensure you're on master branch
git checkout master
git pull origin master

# 2. Install dependencies
npm install

# 3. Create minor version
npx nx release version minor --git-commit=true --git-tag=true

# 4. Build the library
npm run build

# 5. Publish to registry
npx nx release publish

# 6. Push changes
git push origin master --tags
```

### Manual Patch Release

For critical bug fixes that require patch-level increments:

```bash
# On master branch
npx nx release version patch --git-commit=true --git-tag=true
npm run build
npx nx release publish
git push origin master --tags
```

---

## Bug Fix Policy

### Post-Release Bug Fixes

When bugs are discovered **after merging to `master`** (during QA or production):

1. **Create Hotfix Branch**

   ```bash
   git checkout master
   git pull origin master
   git checkout -b hotfix/bug-description
   ```

2. **Implement Fix**

   - Make necessary code changes
   - Add/update tests to prevent regression
   - Ensure all tests pass: `npm run lint && npm test`

3. **Apply Hotfix**

   ```bash
   # Merge hotfix directly to master
   git checkout master
   git merge hotfix/bug-description

   # This triggers automatic patch/minor version and publishing
   git push origin master
   ```

4. **Sync with Development**
   ```bash
   # Merge master back to development to keep branches in sync
   git checkout development
   git merge master
   git push origin development
   ```

### Rationale for Direct Master Fixes

- **Risk Mitigation:** Development branch may contain unrelated changes not ready for production
- **Speed:** Critical fixes can be deployed immediately without waiting for development stabilization
- **Isolation:** Hot fixes remain isolated from ongoing development work

---

## Version Management

### Semantic Versioning Strategy

| Version Type    | When to Use                        | Example           | Branch                    |
| --------------- | ---------------------------------- | ----------------- | ------------------------- |
| **Major**       | Breaking changes, API changes      | `1.0.0` → `2.0.0` | Manual only               |
| **Minor**       | New features, non-breaking changes | `1.0.0` → `1.1.0` | `master` (automatic)      |
| **Patch**       | Bug fixes, small improvements      | `1.0.0` → `1.0.1` | `master` (manual)         |
| **Pre-release** | Development versions               | `1.0.0-next.1`    | `development` (automatic) |

### Version Configuration

The project uses Nx release management with the following configuration:

```json
// nx.json
{
  "release": {
    "version": {
      "generatorOptions": {
        "currentVersionResolver": "git-tag",
        "fallbackCurrentVersionResolver": "disk"
      }
    }
  }
}
```

### Registry Configuration

```json
// package.json
{
  "publishConfig": {
    "@fincloud:registry": "https://nexus-v2.neo.loan/repository/npm/",
    "access": "restricted"
  }
}
```

---

## Best Practices

### Development Workflow

1. **Feature Branches:** Always create feature branches from `development`
2. **Small Commits:** Make atomic commits with clear messages
3. **Testing:** Write tests for new features and bug fixes
4. **Documentation:** Update Storybook stories for component changes
5. **Review:** Use merge requests for all changes

### Release Management

1. **Regular Releases:** Schedule regular releases to avoid large changesets
2. **Communication:** Notify teams before major releases
3. **Documentation:** Keep CHANGELOG.md updated with release notes
4. **Monitoring:** Monitor applications after releases for issues

### Version Strategy

1. **Semantic Versioning:** Follow semantic versioning strictly
2. **Breaking Changes:** Coordinate breaking changes with consuming teams
3. **Deprecation:** Provide deprecation warnings before removing features
4. **Backward Compatibility:** Maintain backward compatibility when possible
