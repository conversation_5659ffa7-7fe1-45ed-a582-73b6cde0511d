# Library Release Strategy

This document describes the branching model, CI/CD pipelines, and versioning approach for publishing the Fincloud UI library to the internal Nexus NPM registry. The strategy ensures that development changes can be tested early while maintaining stability for production releases.

---

## Branching Model

We maintain two permanent branches:

1. **`development`**

   - Main branch for integrating new features and changes.
   - Used for ongoing development work.
   - May contain work that is not yet ready for production.
   - Always corresponds to a published **`next`** version.

2. **`master`**
   - Stable branch representing production-ready code.
   - Only updated via merges from `development` during a web application release process or through hotfixes.
   - Always corresponds to a published **`latest`** version.

---

## CI/CD Pipeline Overview

Both branches trigger automated pipelines when changes are pushed or merged.

### Development Branch Pipeline

- **Trigger:** Merge or push to `development`.
- **Actions:**
  1. Build the library.
  2. Publish to **Nexus private NPM registry** with:
     - **Tag:** `next`
     - **Version format:** `x.x.x-next.x`
  3. Increment the **pre-release version** (the `.next.x` part).
- **Purpose:**  
  Allows testing of unreleased changes in a controlled environment before merging into `master`.

### Master Branch Pipeline

- **Trigger:** Merge or push to `master`.
- **Actions:**
  1. Build the library.
  2. Increment the **minor version** (e.g., `1.4.0` → `1.5.0`).
  3. Publish to **Nexus private NPM registry** with:
     - **Tag:** `latest`
     - **Version format:** standard semantic versioning (e.g., `x.y.z`).
- **Purpose:**  
  Distributes stable releases to be consumed by the web application in QA, staging, and production.

---

## Release Flow

1. During a **web application release**, the `development` branch of the library is merged into `master`.
2. The `master` branch pipeline runs and publishes a new **`latest`** version.
3. The new package version is updated in the web application’s `package.json`.
4. QA validates the release.

---

## Bug Fix Policy

- If a bug is found **after the merge to `master`** (during QA or later):
  - The fix is made **directly on the `master` branch** (hotfix).
  - A new **minor** or **patch** version is published with tag `latest`.
  - Then master is merged in development.
- **Reason:**  
  At the time of the bug discovery, the `development` branch may have moved far ahead, making it risky to use for hotfixes.

---

## Versioning Summary

| Branch      | Tag      | Version Pattern | Version Increment |
| ----------- | -------- | --------------- | ----------------- |
| development | `next`   | `x.x.x-next.x`  | Pre-release       |
| master      | `latest` | `x.y.z`         | Minor/Patch       |
