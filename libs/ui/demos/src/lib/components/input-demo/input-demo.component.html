<div class="fin-flex fin-justify-end">
  <fin-slide-toggle
    [formControl]="editModeControl"
    label="Edit mode"
  ></fin-slide-toggle>
</div>

@for (config of inputConfigs; track $index) {
  <hr class="fin-my-6" finHorizontalSeparator />

  @if (config.infoText) {
    <div>{{ config.infoText }}</div>
  }

  <div>{{ config.fieldCursor }}</div>
  <div class="fin-mb-4">{{ config.textCursor }}</div>

  <fin-input
    [ngClass]="['fin-w-[30rem]', 'fin-block', config.class]"
    [formControl]="inputControl"
    label="Label"
    placeholder="Placeholder"
    [readonly]="!editModeControl.value"
  >
  </fin-input>
}
