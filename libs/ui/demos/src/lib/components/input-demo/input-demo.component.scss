:host {
  font-size: 1.4rem;

  ::ng-deep {
    .example-1 {
      .mat-form-field-appearance-outline {
        &.fin-field-readonly {
          cursor: not-allowed;

          .mat-mdc-input-element {
            cursor: default;
          }
        }
      }
    }

    .example-2 {
      .mat-form-field-appearance-outline {
        &.fin-field-readonly {
          cursor: default;

          .mat-mdc-input-element {
            cursor: default;
          }
        }
      }
    }

    .example-3 {
      .mat-form-field-appearance-outline {
        &.fin-field-readonly {
          cursor: default;

          .mat-mdc-input-element {
            cursor: text;
          }
        }
      }
    }

    .example-4 {
      .mat-form-field-appearance-outline {
        &.fin-field-readonly {
          cursor: not-allowed;

          .mat-mdc-input-element {
            cursor: text;
          }
        }
      }
    }
  }
}
