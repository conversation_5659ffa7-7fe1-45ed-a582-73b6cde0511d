import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { FinInputModule } from '@fincloud/ui/input';
import { FinSeparatorsModule } from '@fincloud/ui/separators';
import { FinSlideToggleModule } from '@fincloud/ui/slide-toggle';

@Component({
  selector: 'fin-input-demo',
  standalone: true,
  imports: [
    CommonModule,
    FinInputModule,
    FinSlideToggleModule,
    FinSeparatorsModule,
  ],
  templateUrl: './input-demo.component.html',
  styleUrl: './input-demo.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinInputDemoComponent {
  editModeControl = new FormControl(false);
  inputControl = new FormControl(
    'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Illo sequi odit modi explicabo itaque id dolorum animi quidem.',
  );
  inputConfigs = [
    {
      infoText: 'Current behavior',
      fieldCursor: 'Field cursor: not-allowed',
      textCursor: 'Text cursor: not-allowed',
      class: '',
    },
    {
      fieldCursor: 'Field cursor: not-allowed',
      textCursor: 'Text cursor: default',
      class: 'example-1',
    },
    {
      fieldCursor: 'Field cursor: default',
      textCursor: 'Text cursor: default',
      class: 'example-2',
    },
    {
      fieldCursor: 'Field cursor: default',
      textCursor: 'Text cursor: text',
      class: 'example-3',
    },
    {
      fieldCursor: 'Field cursor: not-allowed',
      textCursor: 'Text cursor: text',
      class: 'example-4',
    },
  ];
}
